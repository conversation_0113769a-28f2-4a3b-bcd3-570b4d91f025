package lessonList

type LessonListConfigStru []*LessonListGroupStru

type LessonListGroupStru struct {
	GroupKey  string
	GroupName string
	RuleList  []*LessonRuleConfigStru
}

type LessonRuleConfigStru struct {
	Key        string
	Lable      string
	Function   string
	Sort       int
	FusingRule FusingRuleStru
}
type FusingRuleStru struct {
	TimeoutWarning int64 `json:"timeoutWarning"` //熔断报警墙 单位：ms
	TimeoutFusing  int64 `json:"timeoutFusing"`  //熔断墙 单位：ms
	FusingDuration int64 `json:"fusingDuration"` //熔断时长 单位：s
	Duration       int64 `json:"duration"`       //持续时间 单位：s
	DurationTimes  int64 `json:"durationTimes"`  //持续次数
}

var LessonListConfig []*LessonListGroupStru = []*LessonListGroupStru{
	{
		GroupKey:  "coreData",
		GroupName: "核心数据",
		RuleList: []*LessonRuleConfigStru{
			{
				Key:      "lessonId",
				Function: "GetLessonId",
			},
			{
				Key:      "type",
				Function: "GetType",
			},
			{
				Key:      "playType",
				Function: "GetPlayType",
			},
			{
				Key:      "inclassTime",
				Function: "GetInclassTime",
			},
			{
				Key:      "stopTime",
				Function: "GetStopTime",
			},
			{
				Key:      "lessonName",
				Function: "GetLessonName",
			},
			{
				Key:      "startTime",
				Function: "GetStartTime",
			},
			{
				Key:      "preview",
				Function: "GetPreview",
			},
			{
				Key:      "attend",
				Function: "GetAttendData",
			},
			{
				Key:      "playback",
				Function: "GetPlayback",
			},
			{
				Key:      "playbackv1",
				Function: "GetPlaybackOnlineTimeV1",
			},
			{
				Key:      "lbpAttendDuration",
				Function: "GetLbpAttendDuration",
			},
			{
				Key:      "lbpAttendDurationOld",
				Function: "GetLbpAttendDurationOld",
			},
			{
				Key:      "inclassTest",
				Function: "GetInclassTest",
			},
			{
				Key:      "oralQuestion",
				Function: "GetOralQuestion",
			},
			{
				Key:      "homework",
				Function: "GetHomeworkData",
			},
			{
				Key:      "similarHomework",
				Function: "GetHomeworkLikeData",
			},
			{
				Key:      "exercise",
				Function: "GetExerciseColumn",
			},
			{
				Key:      "exerciseAll",
				Function: "GetExerciseAllColumn",
			},
			{
				Key:      "lbpInteractExam",
				Function: "GetLbpInteractExamColumn",
			},
			{
				Key:      "mixPlaybackInteract",
				Function: "GetMixPlaybackInteract",
			},
		},
	},
}
