package lessonList

type FormatFactoryData struct {
	AssistantUid int64                            `json:"assistantUid"`
	PersonUid    int64                            `json:"personUid"`
	CourseId     int64                            `json:"courseId"`
	LessonIds    []int64                          `json:"lessonIds"`
	LeadId       int64                            `json:"leads"`
	StudentUid   int64                            `json:"studentUids"`
	RuleKeys     []string                         `json:"ruleKeys"`
	RuleMap      map[string]*LessonRuleConfigStru `json:"ruleMap"`
	GroupKey     string                           `json:"groupKey" form:"groupKey"`
}

type LeadsDetail struct {
	StudentUid int64
	CourseId   int64
}
