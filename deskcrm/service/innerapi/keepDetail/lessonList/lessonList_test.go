package lessonList

import (
	"deskcrm/api/dal"
	"deskcrm/controllers/http/innerapi/input/inputKeepDetail"
	"deskcrm/controllers/http/innerapi/output/outputKeepDetail"
	"deskcrm/helpers"
	"deskcrm/service/arkBase/dataQuery"
	struArk "deskcrm/stru/ark"
	struLessonList "deskcrm/stru/keepDetail/lessonList"
	"net/http/httptest"
	"path"
	"runtime"
	"testing"

	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
)

// testLessonListService 测试专用的服务结构体，重写了一些方法以避免外部依赖
type testLessonListService struct {
	*lessonListService
}

// 重写 GetLessonList 方法，简化逻辑以便测试
func (s *testLessonListService) GetLessonList(ctx *gin.Context) (lessonDataList *LessonListData, err error) {
	output, _ := s.initOutput(ctx, s.lessonIds)
	for _, lessonID := range s.lessonIds {
		if lesson, ok := s.lessonMap[lessonID]; ok {
			output.LessonListOutput[lessonID] = map[string]interface{}{
				"lessonId":   lessonID,
				"courseId":   s.param.CourseId,
				"startTime":  lesson.StartTime,
				"lessonName": lesson.LessonName,
				"status":     lesson.Status,
			}
		}
	}

	lessonList, err := s.outputToSliceAndSort(ctx, output)
	if err != nil {
		return nil, err
	}

	// 分页处理
	offset, limit := s.param.Offset, s.param.Limit
	total := len(lessonList)
	end := int(offset + limit)
	if end > total {
		end = total
	}
	if int(offset) > total {
		lessonList = []map[string]interface{}{}
	} else {
		lessonList = lessonList[offset:end]
	}

	return &LessonListData{
		LessonList: lessonList,
		lessonIDs:  s.lessonIds,
		Total:      len(s.lessonMap),
	}, nil
}

func getSourcePath() string {
	_, filename, _, _ := runtime.Caller(1)
	return path.Dir(filename)
}

func init() {
	dir := getSourcePath()
	env.SetAppName("testing")
	env.SetRootPath(dir + "/../../../..")

	helpers.PreInit()
	helpers.InitValidator()
	helpers.InitApiClient()
}

// createTestLessonListService 创建测试用的 testLessonListService 实例
func createTestLessonListService() *testLessonListService {
	baseService := &lessonListService{
		param:              &inputKeepDetail.LessonListParam{},
		lessonIds:          make([]int64, 0),
		lessonMap:          map[int64]*dal.LessonInfo{},
		fieldRuleMap:       map[string]*struLessonList.LessonRuleConfigStru{},
		implodeStudentUids: make([]int64, 0),
		sorts:              make([]*struArk.SortsRule, 0),
	}
	return &testLessonListService{
		lessonListService: baseService,
	}
}

// setupTestConfig 设置测试配置
func setupTestConfig() map[string]*struLessonList.LessonRuleConfigStru {
	return map[string]*struLessonList.LessonRuleConfigStru{
		"lessonId": {
			Key:      "lessonId",
			Function: "GetLessonId",
		},
		"lessonName": {
			Key:      "lessonName",
			Function: "GetLessonName",
		},
		"startTime": {
			Key:      "startTime",
			Function: "GetStartTime",
		},
		"status": {
			Key:      "status",
			Function: "GetStatus",
		},
	}
}

func TestLessonListService_GetLessonList_Success(t *testing.T) {
	w := httptest.NewRecorder()
	ctx, _ := gin.CreateTestContext(w)

	// 创建服务实例
	service := createTestLessonListService()

	// 设置测试参数
	param := &inputKeepDetail.LessonListParam{
		AssistantUid: 123456,
		PersonUid:    789012,
		CourseId:     10001,
		StudentUid:   345678,
		LeadsId:      901234,
		Tab:          "coreData",
		Sorts:        `[{"key":"startTime","sort":"asc"}]`,
		Offset:       0,
		Limit:        10,
	}

	var err error
	dataQueryPoint := dataQuery.New()
	leesonListPoint := service
	err = leesonListPoint.InitDataQueryPoint(ctx, dataQueryPoint)
	if err != nil {
		return
	}

	err = leesonListPoint.InitParam(ctx, param)
	if err != nil {
		return
	}

	err = leesonListPoint.InitLessonList(ctx, dataQueryPoint)
	if err != nil {
		return
	}

	if len(leesonListPoint.GetLessonIDs(ctx)) == 0 {
		return
	}

	err = leesonListPoint.InitLessonListConfig(ctx, false)
	if err != nil {
		return
	}

	var lessonDataList *LessonListData
	lessonDataList, err = leesonListPoint.GetLessonList(ctx)
	if err != nil {
		return
	}

	resp := &outputKeepDetail.LessonOutput{
		LessonList: lessonDataList.LessonList,
		Total:      lessonDataList.Total,
	}

	t.Logf("response: %+v", resp)
}
