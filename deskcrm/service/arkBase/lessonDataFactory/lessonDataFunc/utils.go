package lessonDataFunc

import "fmt"

// formatDuration 格式化时长为"XminYs"格式
// 对应PHP中的AssistantDesk_Tools::formatDurationTime方法
func FormatDuration(seconds int64) string {
	remainingSeconds := seconds % 60
	minutes := seconds / 60
	retTime := fmt.Sprintf("%dmin", minutes)
	if remainingSeconds > 0 {
		retTime = retTime + fmt.Sprintf("%ds", remainingSeconds)
	}
	return retTime
}

// LessonDataArray 创建新的课程数据数组，对应PHP中的数组格式 [显示文本, 颜色, 是否可点击]
// 例如：$row['preview'] = ['-', 'gray', 1]
func LessonDataArray(text, color string, clickable int) []interface{} {
	return []interface{}{text, color, clickable}
}
